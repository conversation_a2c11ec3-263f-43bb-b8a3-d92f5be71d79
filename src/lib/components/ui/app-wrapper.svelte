<script lang="ts">
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/utils';

	interface HeaderConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface FooterConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface Props {
		mode: 'normal' | 'sidebar';
		header?: HeaderConfig;
		footer?: FooterConfig;
		breakpoint?: number;

		// Snippets
		headerSnippet?: Snippet;
		sidebarSnippet?: Snippet;
		footerSnippet?: Snippet;
		children: Snippet;
	}

	let {
		mode = 'normal',
		header = { show: true, position: 'default' },
		footer = { show: true, position: 'default' },
		breakpoint = 1024,
		headerSnippet,
		sidebarSnippet,
		footerSnippet,
		children
	}: Props = $props();

	const hasHeader = $derived(header.show && headerSnippet);
	const hasFooter = $derived(footer.show && footerSnippet);
	const hasSidebar = $derived(mode === 'sidebar' && sidebarSnippet);
</script>

<main
	class={cn({
		'relative w-full overflow-x-hidden': mode === 'normal',
		'relative h-dvh min-h-dvh w-full overflow-hidden': mode === 'sidebar',
		'mt-15': (hasHeader && header.position === 'fixed') || header.position === 'adaptable'
	})}
>
	{#if headerSnippet}
		<header
			class={cn({
				'fixed top-0 right-0 z-50 w-full border-b bg-white/70 backdrop-blur-2xl':
					header.position === 'fixed',
				'fixed top-0 right-0 z-50 w-full bg-white lg:static': header.position === 'adaptable',
				default: header.position === 'default'
			})}
		>
			{@render headerSnippet()}
		</header>
	{/if}

	{@render children()}

	{#if hasFooter}
		<footer
			class={cn({
				'fixed right-0 bottom-0 z-50 w-full bg-white': footer.position === 'fixed',
				'fixed right-0 bottom-0 z-50 w-full bg-white lg:relative': footer.position === 'adaptable',
				relative: footer.position === 'default'
			})}
		>
			{@render footerSnippet?.()}
		</footer>
	{/if}
</main>
