<script lang="ts">
	import Header from '$lib/components/landingpage/header.svelte';
	import AppWrapper from '$lib/components/ui/app-wrapper.svelte';
	import Footer from '$lib/components/landingpage/footer.svelte';

	let { children } = $props();
</script>

<AppWrapper
	mode="normal"
	header={{ show: true, position: 'fixed' }}
	footer={{ show: true, position: 'default' }}
>
	{#snippet headerSnippet()}
		<Header />
	{/snippet}

	{@render children()}

	{#snippet footerSnippet()}
		<Footer />
	{/snippet}
</AppWrapper>
